# Configuration file for Enhanced QNA Generator

import os

# API Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCX8Y1DlsAL33OqtyIXIt_VojqmKSKkJIU")

# Content Processing Configuration
MAX_QUESTIONS_PER_CHUNK = 5
MAX_CHUNK_SIZE = 3000  # characters
MIN_CHUNK_SIZE = 500   # characters

# Performance Configuration
MAX_WORKERS = 3  # Number of concurrent threads
REQUEST_DELAY = 2.0  # Seconds between API requests
MAX_RETRIES = 3
TIMEOUT = 30  # Request timeout in seconds

# Quality Thresholds
MIN_CONFIDENCE_THRESHOLD = 0.3
MIN_ANSWER_LENGTH = 10
MAX_ANSWER_LENGTH = 1000

# URLs to process (add more as needed)
ANGRAU_URLS = [
    "https://angrau.ac.in",
    # Add more URLs here:
    # "https://angrau.ac.in/admissions",
    # "https://angrau.ac.in/academics", 
    # "https://angrau.ac.in/research",
    # "https://angrau.ac.in/departments",
    # "https://angrau.ac.in/facilities",
]

# Output Configuration
OUTPUT_FILENAME = "enhanced_angrau_faqs.json"
SIMPLE_OUTPUT_FILENAME = "enhanced_angrau_faqs_simple.json"
LOG_FILENAME = "qna_generator.log"

# Content Filtering
EXCLUDE_PATTERNS = [
    r'cookie',
    r'privacy policy',
    r'terms of service',
    r'copyright',
    r'all rights reserved'
]

# Question Categories
QUESTION_CATEGORIES = {
    'factual': 'Questions about specific facts and information',
    'conceptual': 'Questions about concepts and understanding',
    'practical': 'Questions about real-world applications and procedures'
}
