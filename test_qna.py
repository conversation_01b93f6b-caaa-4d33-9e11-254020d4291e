#!/usr/bin/env python3
"""
Test script for the Enhanced QNA Generator
"""

import json
from scrap import QNAGenerationSystem, QAConfig

def test_basic_functionality():
    """Test basic functionality with a simple example"""
    print("Testing Enhanced QNA Generator...")
    
    # Test configuration
    config = QAConfig(
        max_questions_per_chunk=3,  # Reduced for testing
        max_workers=1,  # Single thread for testing
        request_delay=1.0  # Shorter delay for testing
    )
    
    # Test URLs (you can replace with any accessible URL)
    test_urls = [
        "https://httpbin.org/html",  # Simple test page
        # "https://angrau.ac.in",  # Uncomment to test with actual site
    ]
    
    try:
        # Initialize system
        qna_system = QNAGenerationSystem(config)
        print("✓ System initialized successfully")
        
        # Test scraping
        scraping_result = qna_system.scraper.scrape_url(test_urls[0])
        print(f"✓ Scraping test: {'Success' if scraping_result.success else 'Failed'}")
        
        if scraping_result.success:
            print(f"  - Content length: {len(scraping_result.content)} characters")
            print(f"  - Title: {scraping_result.title}")
        
        # Test content processing
        chunks = qna_system.processor.chunk_content(scraping_result.content, test_urls[0])
        print(f"✓ Content chunking: {len(chunks)} chunks created")
        
        # Test Q&A generation (only if we have content)
        if chunks:
            qa_pairs = qna_system.qa_generator.generate_qa_from_chunk(chunks[0])
            print(f"✓ Q&A generation: {len(qa_pairs)} pairs generated")
            
            if qa_pairs:
                print("  Sample Q&A:")
                print(f"    Q: {qa_pairs[0].question}")
                print(f"    A: {qa_pairs[0].answer[:100]}...")
        
        print("\n✓ All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        return False

def test_full_pipeline():
    """Test the full pipeline with actual processing"""
    print("\nTesting full pipeline...")
    
    config = QAConfig(
        max_questions_per_chunk=2,  # Small number for testing
        max_workers=1,
        request_delay=1.0
    )
    
    # Use a simple, reliable test URL
    test_urls = ["https://httpbin.org/html"]
    
    try:
        qna_system = QNAGenerationSystem(config)
        results = qna_system.process_urls(test_urls)
        
        print(f"✓ Pipeline completed")
        print(f"  - Successful URLs: {results['summary']['successful_urls']}")
        print(f"  - Failed URLs: {results['summary']['failed_urls']}")
        print(f"  - Total Q&A pairs: {results['summary']['total_qa_pairs']}")
        
        # Save test results
        with open("test_results.json", "w") as f:
            json.dump(results, f, indent=2)
        print("✓ Test results saved to test_results.json")
        
        return True
        
    except Exception as e:
        print(f"✗ Pipeline test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Enhanced QNA Generator - Test Suite")
    print("=" * 50)
    
    # Test basic functionality
    basic_test = test_basic_functionality()
    
    # Test full pipeline if basic tests pass
    if basic_test:
        pipeline_test = test_full_pipeline()
    else:
        pipeline_test = False
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Basic functionality: {'✓ PASS' if basic_test else '✗ FAIL'}")
    print(f"Full pipeline: {'✓ PASS' if pipeline_test else '✗ FAIL'}")
    
    if basic_test and pipeline_test:
        print("\n🎉 All tests passed! The system is ready to use.")
        print("\nTo run the full QNA generation:")
        print("python scrap.py")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration and API key.")
        print("\nTroubleshooting:")
        print("1. Make sure your Gemini API key is set correctly")
        print("2. Check your internet connection")
        print("3. Verify all dependencies are installed: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
