import requests
from bs4 import BeautifulSoup
import time
import json
import os
import re
import hashlib
import logging
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
import string

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qna_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === Configuration ===
@dataclass
class QAConfig:
    gemini_api_key: str = os.getenv("GEMINI_API_KEY", "AIzaSyCX8Y1DlsAL33OqtyIXIt_VojqmKSKkJIU")
    max_questions_per_chunk: int = 5
    max_chunk_size: int = 3000  # characters
    min_chunk_size: int = 500   # characters
    max_workers: int = 3
    request_delay: float = 2.0
    max_retries: int = 3
    timeout: int = 30

@dataclass
class ContentChunk:
    text: str
    source_url: str
    chunk_id: str
    word_count: int

@dataclass
class QAPair:
    question: str
    answer: str
    source_url: str
    chunk_id: str
    confidence: float = 0.0
    category: str = "general"
    difficulty: str = "medium"
    timestamp: str = ""

@dataclass
class ScrapingResult:
    url: str
    content: str
    title: str
    success: bool
    error_message: str = ""
    content_type: str = "html"

# === Enhanced Content Extraction ===
class EnhancedWebScraper:
    def __init__(self, config: QAConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def scrape_url(self, url: str) -> ScrapingResult:
        """Enhanced web scraping with better error handling and content extraction"""
        try:
            logger.info(f"Scraping URL: {url}")
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'footer', 'header', 'aside']):
                element.decompose()

            # Extract title
            title = ""
            if soup.title:
                title = soup.title.get_text().strip()
            elif soup.find('h1'):
                title = soup.find('h1').get_text().strip()

            # Extract meaningful content with priority
            content_parts = []

            # High priority content
            for tag in soup.find_all(['h1', 'h2', 'h3']):
                text = self._clean_text(tag.get_text())
                if text and len(text) > 10:
                    content_parts.append(f"HEADING: {text}")

            # Medium priority content
            for tag in soup.find_all(['p', 'div']):
                text = self._clean_text(tag.get_text())
                if text and len(text) > 20:
                    content_parts.append(text)

            # Lists and structured content
            for tag in soup.find_all(['li', 'td']):
                text = self._clean_text(tag.get_text())
                if text and len(text) > 10:
                    content_parts.append(text)

            content = "\n".join(content_parts)

            return ScrapingResult(
                url=url,
                content=content,
                title=title,
                success=True,
                content_type="html"
            )

        except requests.RequestException as e:
            logger.error(f"Request error for {url}: {str(e)}")
            return ScrapingResult(url=url, content="", title="", success=False, error_message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error for {url}: {str(e)}")
            return ScrapingResult(url=url, content="", title="", success=False, error_message=str(e))

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""

        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove very short or meaningless text
        if len(text) < 5:
            return ""

        # Remove common noise patterns
        noise_patterns = [
            r'^(click here|read more|learn more|see more)$',
            r'^\d+$',  # Just numbers
            r'^[^\w\s]+$',  # Just punctuation
        ]

        for pattern in noise_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return ""

        return text

class ContentProcessor:
    def __init__(self, config: QAConfig):
        self.config = config
        try:
            self.stop_words = set(stopwords.words('english'))
        except:
            self.stop_words = set()

    def chunk_content(self, content: str, source_url: str) -> List[ContentChunk]:
        """Split content into manageable chunks for Q&A generation"""
        if not content or len(content) < self.config.min_chunk_size:
            return []

        chunks = []

        # Try to split by sentences first
        try:
            sentences = sent_tokenize(content)
        except:
            # Fallback to simple splitting
            sentences = content.split('. ')

        current_chunk = ""
        chunk_count = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > self.config.max_chunk_size:
                if current_chunk and len(current_chunk) >= self.config.min_chunk_size:
                    # Save current chunk
                    chunk_id = hashlib.md5(f"{source_url}_{chunk_count}".encode()).hexdigest()[:8]
                    chunks.append(ContentChunk(
                        text=current_chunk.strip(),
                        source_url=source_url,
                        chunk_id=chunk_id,
                        word_count=len(current_chunk.split())
                    ))
                    chunk_count += 1

                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence

        # Add the last chunk if it's substantial
        if current_chunk and len(current_chunk) >= self.config.min_chunk_size:
            chunk_id = hashlib.md5(f"{source_url}_{chunk_count}".encode()).hexdigest()[:8]
            chunks.append(ContentChunk(
                text=current_chunk.strip(),
                source_url=source_url,
                chunk_id=chunk_id,
                word_count=len(current_chunk.split())
            ))

        logger.info(f"Created {len(chunks)} chunks from {source_url}")
        return chunks

    def assess_content_quality(self, text: str) -> float:
        """Assess the quality of content for Q&A generation"""
        if not text:
            return 0.0

        score = 0.0

        # Length score (optimal range)
        length = len(text)
        if 500 <= length <= 3000:
            score += 0.3
        elif 200 <= length < 500 or 3000 < length <= 5000:
            score += 0.2
        elif length > 100:
            score += 0.1

        # Sentence structure score
        sentences = text.count('.') + text.count('!') + text.count('?')
        if sentences > 0:
            avg_sentence_length = length / sentences
            if 10 <= avg_sentence_length <= 50:
                score += 0.2

        # Information density score
        words = text.split()
        if words:
            unique_words = set(word.lower() for word in words if word.isalpha())
            if self.stop_words:
                content_words = unique_words - self.stop_words
                if len(words) > 0:
                    density = len(content_words) / len(words)
                    score += min(density * 0.3, 0.3)

        # Question indicators (good for FAQ content)
        question_indicators = ['what', 'how', 'why', 'when', 'where', 'who', 'which']
        indicator_count = sum(1 for indicator in question_indicators if indicator in text.lower())
        score += min(indicator_count * 0.05, 0.2)

        return min(score, 1.0)

# === Enhanced Q&A Generation ===
class EnhancedQAGenerator:
    def __init__(self, config: QAConfig):
        self.config = config
        self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={config.gemini_api_key}"

    def generate_qa_from_chunk(self, chunk: ContentChunk) -> List[QAPair]:
        """Generate Q&A pairs from a content chunk with enhanced prompting"""

        # Assess content and adjust question count
        quality_score = self._assess_chunk_quality(chunk)
        adjusted_questions = max(1, int(self.config.max_questions_per_chunk * quality_score))

        prompts = self._create_diverse_prompts(chunk, adjusted_questions)
        all_qa_pairs = []

        for prompt_type, prompt in prompts.items():
            qa_pairs = self._call_gemini_api(prompt, chunk, prompt_type)
            all_qa_pairs.extend(qa_pairs)

            # Add delay between API calls
            time.sleep(self.config.request_delay)

        # Remove duplicates and return best questions
        unique_qa_pairs = self._remove_duplicate_questions(all_qa_pairs)
        return unique_qa_pairs[:self.config.max_questions_per_chunk * 2]  # Allow some extras for filtering

    def _create_diverse_prompts(self, chunk: ContentChunk, max_questions: int) -> Dict[str, str]:
        """Create different types of prompts for diverse Q&A generation"""

        base_content = chunk.text[:2000]  # Limit content length for API

        prompts = {
            "factual": f"""
Generate {max_questions} factual questions and comprehensive answers based on this content.
Focus on specific facts, figures, processes, and concrete information.

CONTENT:
{base_content}

Format each Q&A as:
Q: [Clear, specific question]
A: [Detailed, accurate answer]

Requirements:
- Questions should be answerable from the content
- Answers should be comprehensive but concise
- Focus on factual information
- Avoid yes/no questions
""",

            "conceptual": f"""
Generate {max_questions} conceptual questions that test understanding of key concepts and ideas.
Focus on explanations, relationships, and deeper understanding.

CONTENT:
{base_content}

Format each Q&A as:
Q: [Conceptual question starting with "What is", "How does", "Why is", etc.]
A: [Clear explanation with context]

Requirements:
- Questions should explore concepts and relationships
- Answers should explain the "why" and "how"
- Include relevant context and examples
""",

            "practical": f"""
Generate {max_questions} practical questions that someone might actually ask about this topic.
Focus on real-world applications and user concerns.

CONTENT:
{base_content}

Format each Q&A as:
Q: [Practical question someone would actually ask]
A: [Helpful, actionable answer]

Requirements:
- Questions should be realistic and useful
- Answers should be practical and actionable
- Consider what users really want to know
"""
        }

        return prompts

    def _call_gemini_api(self, prompt: str, chunk: ContentChunk, prompt_type: str) -> List[QAPair]:
        """Call Gemini API with retry logic"""

        for attempt in range(self.config.max_retries):
            try:
                payload = {
                    "contents": [{"parts": [{"text": prompt}]}],
                    "generationConfig": {
                        "temperature": 0.7,
                        "topK": 40,
                        "topP": 0.95,
                        "maxOutputTokens": 2048,
                    }
                }

                response = requests.post(self.api_url, json=payload, timeout=self.config.timeout)
                response.raise_for_status()

                result = response.json()
                generated_text = result['candidates'][0]['content']['parts'][0]['text']

                # Parse the generated Q&A pairs
                qa_pairs = self._parse_qa_response(generated_text, chunk, prompt_type)
                logger.info(f"Generated {len(qa_pairs)} Q&A pairs for chunk {chunk.chunk_id} using {prompt_type} prompt")

                return qa_pairs

            except requests.RequestException as e:
                logger.warning(f"API request failed (attempt {attempt + 1}): {str(e)}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"Failed to generate Q&A after {self.config.max_retries} attempts")
                    return []
            except Exception as e:
                logger.error(f"Unexpected error in API call: {str(e)}")
                return []

        return []

    def _parse_qa_response(self, response_text: str, chunk: ContentChunk, prompt_type: str) -> List[QAPair]:
        """Parse the API response into structured Q&A pairs"""
        qa_pairs = []

        # Split by Q: or A: patterns
        lines = response_text.split('\n')
        current_question = ""
        current_answer = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line.startswith('Q:') or line.startswith('Q1:') or line.startswith('Question:'):
                # Save previous Q&A if exists
                if current_question and current_answer:
                    qa_pairs.append(self._create_qa_pair(current_question, current_answer, chunk, prompt_type))

                current_question = re.sub(r'^Q\d*:\s*', '', line).strip()
                current_answer = ""

            elif line.startswith('A:') or line.startswith('A1:') or line.startswith('Answer:'):
                current_answer = re.sub(r'^A\d*:\s*', '', line).strip()

            elif current_answer:
                # Continue building the answer
                current_answer += " " + line
            elif current_question and not current_answer:
                # This might be a continuation of the question
                current_question += " " + line

        # Don't forget the last Q&A pair
        if current_question and current_answer:
            qa_pairs.append(self._create_qa_pair(current_question, current_answer, chunk, prompt_type))

        return qa_pairs

    def _create_qa_pair(self, question: str, answer: str, chunk: ContentChunk, prompt_type: str) -> QAPair:
        """Create a structured Q&A pair with metadata"""

        # Clean up question and answer
        question = question.strip().rstrip('?') + '?'
        answer = answer.strip()

        # Assess confidence based on answer quality
        confidence = self._assess_answer_quality(question, answer, chunk.text)

        # Determine difficulty
        difficulty = self._assess_question_difficulty(question, answer)

        return QAPair(
            question=question,
            answer=answer,
            source_url=chunk.source_url,
            chunk_id=chunk.chunk_id,
            confidence=confidence,
            category=prompt_type,
            difficulty=difficulty,
            timestamp=datetime.now().isoformat()
        )

    def _assess_chunk_quality(self, chunk: ContentChunk) -> float:
        """Assess the quality of a content chunk for Q&A generation"""
        score = 0.0

        # Length score
        if 500 <= len(chunk.text) <= 2000:
            score += 0.4
        elif 200 <= len(chunk.text) < 500:
            score += 0.3
        elif len(chunk.text) > 100:
            score += 0.2

        # Information density
        sentences = chunk.text.count('.') + chunk.text.count('!') + chunk.text.count('?')
        if sentences > 0:
            score += min(sentences * 0.1, 0.3)

        # Keyword indicators for good content
        good_indicators = ['what', 'how', 'why', 'process', 'method', 'procedure', 'steps', 'requirements']
        indicator_count = sum(1 for indicator in good_indicators if indicator.lower() in chunk.text.lower())
        score += min(indicator_count * 0.05, 0.3)

        return min(score, 1.0)

    def _assess_answer_quality(self, question: str, answer: str, source_text: str) -> float:
        """Assess the quality/confidence of a generated answer"""
        if not answer or len(answer) < 10:
            return 0.1

        score = 0.5  # Base score

        # Length appropriateness
        if 50 <= len(answer) <= 500:
            score += 0.2
        elif 20 <= len(answer) < 50:
            score += 0.1

        # Check if answer seems to be based on source content
        question_words = set(question.lower().split())
        answer_words = set(answer.lower().split())
        source_words = set(source_text.lower().split())

        # Overlap with source content
        source_overlap = len(answer_words.intersection(source_words)) / len(answer_words) if answer_words else 0
        score += min(source_overlap * 0.2, 0.2)

        # Relevance to question
        question_overlap = len(question_words.intersection(answer_words)) / len(question_words) if question_words else 0
        score += min(question_overlap * 0.1, 0.1)

        return min(score, 1.0)

    def _assess_question_difficulty(self, question: str, answer: str) -> str:
        """Assess the difficulty level of a question"""
        question_lower = question.lower()
        answer_length = len(answer.split())  # Use answer length as a factor

        # Easy questions
        easy_patterns = ['what is', 'who is', 'when is', 'where is']
        if any(pattern in question_lower for pattern in easy_patterns):
            return "easy"

        # Hard questions
        hard_patterns = ['analyze', 'evaluate', 'compare', 'contrast', 'justify', 'critique']
        if any(pattern in question_lower for pattern in hard_patterns):
            return "hard"

        # Consider answer length for difficulty assessment
        if answer_length > 100:  # Very long answers might indicate complex questions
            return "hard"
        elif answer_length < 20:  # Very short answers might be simple facts
            return "easy"

        # Medium by default
        return "medium"

    def _remove_duplicate_questions(self, qa_pairs: List[QAPair]) -> List[QAPair]:
        """Remove duplicate or very similar questions"""
        if not qa_pairs:
            return []

        unique_pairs = []
        seen_questions = set()

        for qa_pair in qa_pairs:
            # Normalize question for comparison
            normalized_q = re.sub(r'[^\w\s]', '', qa_pair.question.lower()).strip()
            normalized_q = ' '.join(normalized_q.split())  # Normalize whitespace

            # Check for exact duplicates
            if normalized_q in seen_questions:
                continue

            # Check for very similar questions (simple similarity check)
            is_similar = False
            for seen_q in seen_questions:
                if self._questions_similar(normalized_q, seen_q):
                    is_similar = True
                    break

            if not is_similar:
                seen_questions.add(normalized_q)
                unique_pairs.append(qa_pair)

        return unique_pairs

    def _questions_similar(self, q1: str, q2: str, threshold: float = 0.8) -> bool:
        """Check if two questions are similar"""
        words1 = set(q1.split())
        words2 = set(q2.split())

        if not words1 or not words2:
            return False

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        similarity = len(intersection) / len(union) if union else 0
        return similarity >= threshold

# === Main QNA Generation System ===
class QNAGenerationSystem:
    def __init__(self, config: QAConfig = None):
        self.config = config or QAConfig()
        self.scraper = EnhancedWebScraper(self.config)
        self.processor = ContentProcessor(self.config)
        self.qa_generator = EnhancedQAGenerator(self.config)

    def process_urls(self, urls: List[str]) -> Dict:
        """Process multiple URLs and generate comprehensive Q&A"""
        logger.info(f"Starting Q&A generation for {len(urls)} URLs")

        all_results = {
            "metadata": {
                "generation_time": datetime.now().isoformat(),
                "total_urls": len(urls),
                "config": {
                    "max_questions_per_chunk": self.config.max_questions_per_chunk,
                    "max_chunk_size": self.config.max_chunk_size,
                    "min_chunk_size": self.config.min_chunk_size
                }
            },
            "results": [],
            "summary": {
                "successful_urls": 0,
                "failed_urls": 0,
                "total_chunks": 0,
                "total_qa_pairs": 0
            }
        }

        # Process URLs with concurrent execution
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            future_to_url = {executor.submit(self._process_single_url, url): url for url in urls}

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    result = future.result()
                    all_results["results"].append(result)

                    if result["success"]:
                        all_results["summary"]["successful_urls"] += 1
                        all_results["summary"]["total_chunks"] += result["total_chunks"]
                        all_results["summary"]["total_qa_pairs"] += result["total_qa_pairs"]
                    else:
                        all_results["summary"]["failed_urls"] += 1

                except Exception as e:
                    logger.error(f"Error processing {url}: {str(e)}")
                    all_results["results"].append({
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "qa_pairs": []
                    })
                    all_results["summary"]["failed_urls"] += 1

        logger.info(f"Q&A generation completed. Success: {all_results['summary']['successful_urls']}, Failed: {all_results['summary']['failed_urls']}")
        return all_results

    def _process_single_url(self, url: str) -> Dict:
        """Process a single URL and generate Q&A"""
        logger.info(f"Processing URL: {url}")

        # Step 1: Scrape content
        scraping_result = self.scraper.scrape_url(url)
        if not scraping_result.success:
            return {
                "url": url,
                "success": False,
                "error": scraping_result.error_message,
                "qa_pairs": [],
                "total_chunks": 0,
                "total_qa_pairs": 0
            }

        # Step 2: Process and chunk content
        chunks = self.processor.chunk_content(scraping_result.content, url)
        if not chunks:
            return {
                "url": url,
                "success": False,
                "error": "No meaningful content found or content too short",
                "qa_pairs": [],
                "total_chunks": 0,
                "total_qa_pairs": 0
            }

        # Step 3: Generate Q&A for each chunk
        all_qa_pairs = []
        for chunk in chunks:
            try:
                qa_pairs = self.qa_generator.generate_qa_from_chunk(chunk)
                all_qa_pairs.extend(qa_pairs)
                time.sleep(self.config.request_delay)  # Rate limiting
            except Exception as e:
                logger.warning(f"Failed to generate Q&A for chunk {chunk.chunk_id}: {str(e)}")

        # Step 4: Post-process and filter Q&A pairs
        filtered_qa_pairs = self._filter_and_rank_qa_pairs(all_qa_pairs)

        return {
            "url": url,
            "title": scraping_result.title,
            "success": True,
            "total_chunks": len(chunks),
            "total_qa_pairs": len(filtered_qa_pairs),
            "qa_pairs": [self._qa_pair_to_dict(qa) for qa in filtered_qa_pairs]
        }

    def _filter_and_rank_qa_pairs(self, qa_pairs: List[QAPair]) -> List[QAPair]:
        """Filter and rank Q&A pairs by quality"""
        if not qa_pairs:
            return []

        # Filter by minimum confidence
        filtered_pairs = [qa for qa in qa_pairs if qa.confidence >= 0.3]

        # Sort by confidence (descending)
        filtered_pairs.sort(key=lambda x: x.confidence, reverse=True)

        # Limit total number
        max_total = self.config.max_questions_per_chunk * 10  # Allow more for variety
        return filtered_pairs[:max_total]

    def _qa_pair_to_dict(self, qa_pair: QAPair) -> Dict:
        """Convert QAPair to dictionary for JSON serialization"""
        return {
            "question": qa_pair.question,
            "answer": qa_pair.answer,
            "source_url": qa_pair.source_url,
            "chunk_id": qa_pair.chunk_id,
            "confidence": round(qa_pair.confidence, 3),
            "category": qa_pair.category,
            "difficulty": qa_pair.difficulty,
            "timestamp": qa_pair.timestamp
        }

    def save_results(self, results: Dict, filename: str = "enhanced_angrau_faqs.json"):
        """Save results to JSON file with enhanced formatting"""
        try:
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"Results saved to {filename}")

            # Also save a simplified version for easy reading
            simplified_filename = filename.replace('.json', '_simple.json')
            simplified_results = self._create_simplified_output(results)
            with open(simplified_filename, "w", encoding="utf-8") as f:
                json.dump(simplified_results, f, indent=2, ensure_ascii=False)
            logger.info(f"Simplified results saved to {simplified_filename}")

        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")

    def _create_simplified_output(self, results: Dict) -> List[Dict]:
        """Create a simplified output format for easy reading"""
        simplified = []
        for result in results["results"]:
            if result["success"]:
                for qa in result["qa_pairs"]:
                    simplified.append({
                        "question": qa["question"],
                        "answer": qa["answer"],
                        "source": result["url"],
                        "confidence": qa["confidence"],
                        "category": qa["category"]
                    })
        return simplified

def main():
    """Enhanced main function with better error handling and logging"""
    try:
        # Configuration
        config = QAConfig()

        # URLs to process - you can add more ANGRAU pages here
        urls = [
            "https://angrau.ac.in",
            # Add more URLs as needed
            # "https://angrau.ac.in/admissions",
            # "https://angrau.ac.in/academics",
            # "https://angrau.ac.in/research",
        ]

        # Reduce questions for faster completion
        config.max_questions_per_chunk = 3

        # Initialize the system
        qna_system = QNAGenerationSystem(config)

        # Process URLs and generate Q&A
        logger.info("Starting enhanced Q&A generation process...")
        results = qna_system.process_urls(urls)

        # Save results
        qna_system.save_results(results)

        # Print summary
        summary = results["summary"]
        print(f"\n=== Q&A Generation Summary ===")
        print(f"Total URLs processed: {summary['successful_urls'] + summary['failed_urls']}")
        print(f"Successful: {summary['successful_urls']}")
        print(f"Failed: {summary['failed_urls']}")
        print(f"Total content chunks: {summary['total_chunks']}")
        print(f"Total Q&A pairs generated: {summary['total_qa_pairs']}")
        print(f"\nResults saved to enhanced_angrau_faqs.json and enhanced_angrau_faqs_simple.json")

    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
