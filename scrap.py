import requests
from bs4 import BeautifulSoup
import openai
import time
import json

# === 1. Setup OpenAI API ===
openai.api_key = 'your_openai_api_key_here'  # Replace with your API key

# === 2. Function to scrape page content ===
def scrape_text_from_url(url):
    res = requests.get(url)
    soup = BeautifulSoup(res.content, 'html.parser')
    # Collect meaningful content: h1, h2, p tags
    texts = []
    for tag in soup.find_all(['h1', 'h2', 'h3', 'p', 'li']):
        if tag.text.strip():
            texts.append(tag.text.strip())
    return "\n".join(texts)

# === 3. Function to generate Q&A using Google Gemini API ===
def generate_qa_from_text(text, max_questions=5):
    import requests
    import os
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCX8Y1DlsAL33OqtyIXIt_VojqmKSKkJIU")  # Replace with your Gemini API key or set as env var
    API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" + GEMINI_API_KEY
    prompt = f"""
You are an assistant creating a chatbot. Based on the following content, generate {max_questions} useful queries and answers in FAQ format.

CONTENT:
{text}

Format:
Q1: <question>
A1: <answer>
...

Only output the Q&A, no extra explanation.
"""
    payload = {
        "contents": [{"parts": [{"text": prompt}]}]
    }
    response = requests.post(API_URL, json=payload)
    if response.status_code == 200:
        result = response.json()
        # Gemini returns the result in 'candidates' -> 'content' -> 'parts' -> 'text'
        try:
            return result['candidates'][0]['content']['parts'][0]['text']
        except Exception:
            return str(result)
    else:
        return f"Error: {response.status_code} - {response.text}"

# === 4. Main process ===
def main():
    urls = [
        "https://angrau.ac.in",  # You can add more ANGRAU pages here
    ]
    final_qa = []

    for url in urls:
        print(f"Scraping: {url}")
        text = scrape_text_from_url(url)
        print(text)
        print("Generating Q&A...")
        qa = generate_qa_from_text(text, max_questions=7)
        final_qa.append({"url": url, "qa": qa})
        time.sleep(3)  # Avoid hitting API limits too quickly

    # === 5. Save to file ===
    with open("angrau_faqs.json", "w", encoding="utf-8") as f:
        json.dump(final_qa, f, indent=2, ensure_ascii=False)

    print("FAQs saved to angrau_faqs.json")

if __name__ == "__main__":
    main()
