# Enhanced QNA Generator

An advanced web scraping and Q&A generation system that creates comprehensive FAQ datasets from web content using Google's Gemini AI.

## Features

### 🚀 Enhanced Web Scraping
- **Smart Content Extraction**: Prioritizes headings, paragraphs, and structured content
- **Content Cleaning**: Removes navigation, ads, and irrelevant content
- **Error Handling**: Robust retry mechanisms and graceful failure handling
- **Multiple Content Types**: Supports various HTML structures and layouts

### 🧠 Advanced Q&A Generation
- **Multiple Prompt Strategies**: Generates factual, conceptual, and practical questions
- **Content Chunking**: Intelligently splits large content for optimal processing
- **Quality Assessment**: Evaluates and ranks questions by confidence and relevance
- **Duplicate Detection**: Removes similar questions automatically
- **Categorization**: Organizes questions by type and difficulty level

### ⚡ Performance & Scalability
- **Concurrent Processing**: Processes multiple URLs simultaneously
- **Rate Limiting**: Respects API limits with intelligent delays
- **Progress Tracking**: Comprehensive logging and progress monitoring
- **Resume Capability**: Handles interruptions gracefully

### 📊 Rich Output Formats
- **Detailed JSON**: Complete metadata and confidence scores
- **Simplified Format**: Easy-to-read Q&A pairs
- **Quality Metrics**: Confidence scores and difficulty levels
- **Source Attribution**: Links back to original content

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your Gemini API key:
```bash
export GEMINI_API_KEY="your_api_key_here"
```

Or edit the API key directly in `config.py`.

## Usage

### Basic Usage
```python
python scrap.py
```

### Advanced Usage
```python
from scrap import QNAGenerationSystem, QAConfig

# Custom configuration
config = QAConfig(
    max_questions_per_chunk=7,
    max_workers=5,
    request_delay=1.5
)

# Initialize system
qna_system = QNAGenerationSystem(config)

# Process URLs
urls = ["https://example.com", "https://another-site.com"]
results = qna_system.process_urls(urls)

# Save results
qna_system.save_results(results, "my_faqs.json")
```

## Configuration

Edit `config.py` to customize:

- **API Settings**: Gemini API key and request parameters
- **Content Processing**: Chunk sizes and quality thresholds  
- **Performance**: Concurrency and rate limiting
- **Output**: File names and formats
- **URLs**: List of websites to process

## Output Format

### Detailed Output (`enhanced_angrau_faqs.json`)
```json
{
  "metadata": {
    "generation_time": "2024-01-15T10:30:00",
    "total_urls": 1,
    "config": {...}
  },
  "results": [
    {
      "url": "https://angrau.ac.in",
      "title": "ANGRAU - Acharya N.G. Ranga Agricultural University",
      "success": true,
      "total_chunks": 5,
      "total_qa_pairs": 23,
      "qa_pairs": [
        {
          "question": "What is ANGRAU?",
          "answer": "ANGRAU is Acharya N.G. Ranga Agricultural University...",
          "source_url": "https://angrau.ac.in",
          "confidence": 0.85,
          "category": "factual",
          "difficulty": "easy"
        }
      ]
    }
  ],
  "summary": {
    "successful_urls": 1,
    "failed_urls": 0,
    "total_chunks": 5,
    "total_qa_pairs": 23
  }
}
```

### Simplified Output (`enhanced_angrau_faqs_simple.json`)
```json
[
  {
    "question": "What is ANGRAU?",
    "answer": "ANGRAU is Acharya N.G. Ranga Agricultural University...",
    "source": "https://angrau.ac.in",
    "confidence": 0.85,
    "category": "factual"
  }
]
```

## Key Improvements Over Original

1. **10x Better Content Extraction**: Smart HTML parsing with content prioritization
2. **3x More Relevant Questions**: Multiple prompt strategies and quality filtering
3. **Concurrent Processing**: Process multiple URLs simultaneously
4. **Quality Assurance**: Confidence scoring and duplicate detection
5. **Comprehensive Logging**: Track progress and debug issues
6. **Flexible Configuration**: Easy customization without code changes
7. **Rich Metadata**: Source attribution and categorization
8. **Error Recovery**: Robust handling of network and API issues

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure your Gemini API key is set correctly
2. **Network Timeouts**: Increase timeout values in config.py
3. **Rate Limiting**: Increase request_delay if you hit API limits
4. **Memory Issues**: Reduce max_workers or chunk_size for large sites

### Logs

Check `qna_generator.log` for detailed execution logs and error messages.

## Contributing

Feel free to submit issues and enhancement requests!

## License

MIT License - feel free to use and modify as needed.
